import * as React from 'react';
import { Button, XStack, Text } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { Modal } from 'react-native';
import { useTranslation } from 'react-i18next';
import { AIChatGUI } from './AIChatGUI';

interface ChatNavigationButtonProps {
  variant?: 'floating' | 'inline';
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
}

export const ChatNavigationButton: React.FC<ChatNavigationButtonProps> = ({
  variant = 'floating',
  size = 'medium',
  showText = true,
}) => {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  const handleNavigateToChat = () => {
    setIsModalVisible(true);
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small': return '$3';
      case 'large': return '$5';
      default: return '$4';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'large': return 28;
      default: return 20;
    }
  };

  if (variant === 'floating') {
    return (
      <>
        <Button
          position="absolute"
          bottom="$6"
          right="$4"
          circular={!showText}
          borderRadius={showText ? "$6" : undefined}
          size={getButtonSize()}
          backgroundColor="$purple10"
          elevation={6}
          shadowColor="$purple10"
          shadowOffset={{ width: 0, height: 4 }}
          shadowOpacity={0.3}
          shadowRadius={8}
          onPress={handleNavigateToChat}
          animation="quick"
          pressStyle={{ scale: 0.95 }}
          zIndex={1000}
        >
          <XStack alignItems="center" gap="$2">
            <Ionicons name="chatbubble-ellipses" size={getIconSize()} color="white" />
            {showText && (
              <Text color="white" fontSize="$3" fontWeight="600">
                {t('navigation.aiAssistant', { defaultValue: 'AI Assistant' })}
              </Text>
            )}
          </XStack>
        </Button>

        <Modal
          visible={isModalVisible}
          animationType="slide"
          presentationStyle="fullScreen"
          onRequestClose={() => setIsModalVisible(false)}
        >
          <AIChatGUI onClose={() => setIsModalVisible(false)} />
        </Modal>
      </>
    );
  }

  return (
    <>
      <Button
        backgroundColor="$purple10"
        borderRadius="$4"
        size={getButtonSize()}
        onPress={handleNavigateToChat}
        animation="quick"
        pressStyle={{ scale: 0.98 }}
      >
        <XStack alignItems="center" gap="$2">
          <Ionicons name="chatbubble-ellipses" size={getIconSize()} color="white" />
          {showText && (
            <Text color="white" fontSize="$3" fontWeight="600">
              {t('ai.chatWithAI', { defaultValue: 'Chat with AI' })}
            </Text>
          )}
        </XStack>
      </Button>

      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <AIChatGUI onClose={() => setIsModalVisible(false)} />
      </Modal>
    </>
  );
};

// Quick access component for main pages
export const QuickChatAccess: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  return (
    <>
    <XStack 
      position="absolute" 
      top="$4" 
      right="$4" 
      zIndex={100}
    >
      <ChatNavigationButton 
        variant="inline" 
        size="small" 
        showText={false} 
      />
    </XStack>

    <Modal
      visible={isModalVisible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={() => setIsModalVisible(false)}
    >
      <AIChatGUI onClose={() => setIsModalVisible(false)} />
    </Modal>
    </>
  );
};

// Banner component for promoting AI chat
export const AIChatPromoBanner: React.FC<{ onDismiss?: () => void }> = ({ onDismiss }) => {
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  return (
    <>
    <XStack
      backgroundColor="$purple2"
      padding="$3"
      margin="$3"
      borderRadius="$4"
      borderWidth={1}
      borderColor="$purple6"
      alignItems="center"
      justifyContent="space-between"
    >
      <XStack alignItems="center" gap="$3" flex={1}>
        <Ionicons name="chatbubble-ellipses" size={24} color="#7529B3" />
        <Text color="$purple11" fontSize="$3" flex={1}>
          Need help? Try our AI Assistant for instant support with orders, deliveries, and more!
        </Text>
      </XStack>
      <XStack gap="$2">
        <Button
          size="$2"
          backgroundColor="$purple10"
          borderRadius="$3"
          onPress={() => setIsModalVisible(true)}
        >
          <Text color="white" fontSize="$2" fontWeight="600">
            Try Now
          </Text>
        </Button>
        {onDismiss && (
          <Button
            size="$2"
            circular
            backgroundColor="$gray4"
            onPress={onDismiss}
          >
            <Ionicons name="close" size={12} color="$gray10" />
          </Button>
        )}
      </XStack>
    </XStack>

    <Modal
      visible={isModalVisible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={() => setIsModalVisible(false)}
    >
      <AIChatGUI onClose={() => setIsModalVisible(false)} />
    </Modal>
    </>
  );
};
