import { useState, useEffect } from 'react';
import { <PERSON><PERSON>View } from 'react-native';
import { Button, Text, View, YStack, XStack, Card, Separator } from 'tamagui';
import { <PERSON><PERSON>View } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useRequestPickupStore } from './useRequestPickupStore';
import { useSendPackageStore } from './useSendPackageStore';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { getUserPackages } from '../../../services/apiService';

export function CustomerPackagesGUI() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'sent' | 'pickup'>('sent');
  const { pickupRequests, addPickupRequest } = useRequestPickupStore();
  const { sendRequests, addSendRequest } = useSendPackageStore();
  const [loading, setLoading] = useState(true);

  // Fetch packages from backend on component mount
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        setLoading(true);
        const { packages: backendPackages } = await getUserPackages();

        // Sync backend packages with local stores
        backendPackages.forEach((pkg: any) => {
          // Determine if it's a send package or pickup request based on data structure
          if (pkg.deliveryAddress) {
            // It's a send package
            const sendPackage = {
              id: pkg.trackingNumber,
              createdAt: pkg.createdAt,
              pickup: {
                address: pkg.pickupAddress.street,
                lat: pkg.pickupAddress.coordinates.lat,
                lng: pkg.pickupAddress.coordinates.lng
              },
              dropoff: {
                address: pkg.deliveryAddress.street,
                lat: pkg.deliveryAddress.coordinates.lat,
                lng: pkg.deliveryAddress.coordinates.lng
              },
              receiverName: pkg.recipientInfo.name,
              receiverPhone: pkg.recipientInfo.phone,
              packageType: pkg.packageDetails.type,
              status: pkg.status,
              estimatedTime: pkg.estimatedDeliveryTime || '30-45 mins',
              driverName: pkg.driverName || 'Driver',
              driverPhone: pkg.driverPhone || '',
              cost: pkg.cost,
              notes: pkg.notes || ''
            };

            // Add to store if not already exists
            if (!sendRequests.find(p => p.id === pkg.trackingNumber)) {
              addSendRequest(sendPackage);
            }
          } else {
            // It's a pickup request
            const pickupRequest = {
              id: pkg.trackingNumber,
              createdAt: pkg.createdAt,
              pickup: {
                address: pkg.pickupAddress.street,
                lat: pkg.pickupAddress.coordinates.lat,
                lng: pkg.pickupAddress.coordinates.lng
              },
              packageType: pkg.packageDetails.type,
              status: pkg.status,
              estimatedTime: pkg.estimatedPickupTime || '45-60 mins',
              driverName: pkg.driverName || 'Driver',
              driverPhone: pkg.driverPhone || '',
              cost: pkg.cost,
              notes: pkg.notes || '',
              itemDescription: pkg.itemDescription || 'N/A', // Add default or fetched value
              preferredTime: pkg.preferredTime || 'Anytime'  // Add default or fetched value
            };

            // Add to store if not already exists
            if (!pickupRequests.find(p => p.id === pkg.trackingNumber)) {
              addPickupRequest(pickupRequest);
            }
          }
        });
      } catch (error) {
        console.error('Error fetching packages:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, [addSendRequest, addPickupRequest, sendRequests, pickupRequests]);

  const data = activeTab === 'sent' ? sendRequests : pickupRequests;

  return (
    <>
      {/* Gradient Header */}
      <View
        width="100%"
        style={{
          paddingVertical: 40,
          paddingHorizontal: 24,
          borderBottomLeftRadius: 32,
          borderBottomRightRadius: 32,
          backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
          backgroundColor: '#7529B3',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
          <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
            📦 {t('packages.myPackages', { defaultValue: 'My Packages' })}
          </Text>
        </MotiView>
      </View>

      <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 120 }}>
        {/* Tabs */}
        <XStack jc="center" mt="$3" mb="$2" gap="$2">
          <Button
            size="$3"
            chromeless
            px="$4"
            py="$2"
            br="$10"
            bg={activeTab === 'sent' ? '$primary' : '$gray4'}
            color={activeTab === 'sent' ? 'white' : '$gray10'}
            onPress={() => setActiveTab('sent')}
          >
            {t('packages.sentPackages', { defaultValue: 'Sent Packages' })}
          </Button>
          <Button
            size="$3"
            chromeless
            px="$4"
            py="$2"
            br="$10"
            bg={activeTab === 'pickup' ? '$primary' : '$gray4'}
            color={activeTab === 'pickup' ? 'white' : '$gray10'}
            onPress={() => setActiveTab('pickup')}
          >
            {t('packages.pickupRequests', { defaultValue: 'Pickup Requests' })}
          </Button>
        </XStack>

        {/* Cards */}
        <YStack px="$4" pt="$2" gap="$4" width="95%" alignSelf='center'>
          {data.length === 0 ? (
            <MotiView from={{ opacity: 0 }} animate={{ opacity: 1 }}>
              <Text textAlign="center" color="$gray9" fontSize="$6" mt="$6">
                😕 {t('packages.noPackagesYet', {
                  type: activeTab === 'sent'
                    ? t('packages.sentPackages', { defaultValue: 'sent packages' }).toLowerCase()
                    : t('packages.pickupRequests', { defaultValue: 'pickup requests' }).toLowerCase(),
                  defaultValue: `No ${activeTab === 'sent' ? 'sent packages' : 'pickup requests'} yet.`
                })}
              </Text>
            </MotiView>
          ) : (
            data.map((item, index) => (
                <MotiView
                    key={index}
                    from={{ opacity: 0, translateY: 10 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ delay: index * 100 }}
                >
                    <Card
                    padded
                    elevate
                    br="$8"
                    bw={1}
                    bc="$gray6"
                    bg="$colorTransparent"
                    style={{
                        overflow: 'hidden',
                        borderWidth: 0,
                        shadowColor: '#000',
                        shadowOpacity: 0.1,
                        shadowRadius: 10,
                        backgroundColor: '#fff',
                    }}
                    >
                    {/* Card Header */}
                    <View
                        px="$4"
                        py="$3"
                        bg="$primary"
                        jc="space-between"
                        ai="center"
                        flexDirection="row"
                        borderTopLeftRadius={12}
                        borderTopRightRadius={12}
                    >
                        <Text color="white" fontWeight="800" fontSize="$6">
                        {activeTab === 'sent'
                          ? `📦 ${t('packages.sentPackages', { defaultValue: 'Sent Package' }).slice(0, -1)}`
                          : `📥 ${t('packages.pickupRequests', { defaultValue: 'Pickup Request' }).slice(0, -1)}`
                        }
                        </Text>
                        <Ionicons name="cube-outline" size={20} color="white" />
                    </View>

                    {/* Card Content */}
                    <YStack p="$4" pb="$0" bg="white" gap="$3" width="95%">
                        {activeTab === 'sent' ? (
                            <>
                            <InfoRow label={t('packages.from', { defaultValue: 'From' })} icon="location-outline" value={item.pickup?.address} />
                            {'dropoff' in item && (
                              <InfoRow label={t('packages.to', { defaultValue: 'To' })} icon="navigate-outline" value={item.dropoff?.address} />
                            )}
                            {'receiverName' in item && 'receiverPhone' in item && (
                              <InfoRow label={t('packages.recipient', { defaultValue: 'Receiver' })} icon="person-outline" value={`${item.receiverName} (${item.receiverPhone})`} />
                            )}
                            {'packageType' in item && (
                              <InfoRow label={t('packages.packageType', { defaultValue: 'Type' })} icon="cube-outline" value={item.packageType} />
                            )}
                            <InfoRow label={t('packages.notes', { defaultValue: 'Notes' })} icon="document-text-outline" value={item.notes || t('packages.none', { defaultValue: 'None' })} />
                            </>
                        ) : (
                            <>
                            <InfoRow label={t('packages.pickupAddress', { defaultValue: 'Pickup' })} icon="location-outline" value={item.pickup?.address} />
                            {'itemDescription' in item && (
                              <InfoRow label={t('packages.item', { defaultValue: 'Item' })} icon="pricetag-outline" value={item.itemDescription} />
                            )}
                            {'preferredTime' in item && (
                              <InfoRow label={t('packages.time', { defaultValue: 'Time' })} icon="time-outline" value={item.preferredTime} />
                            )}
                            <InfoRow label={t('packages.notes', { defaultValue: 'Notes' })} icon="document-text-outline" value={item.notes || t('packages.none', { defaultValue: 'None' })} />
                            </>
                        )}

                        {'status' in item && item.status && (
                            <InfoRow label={t('packages.status', { defaultValue: 'Status' })} icon="car-outline" value={t(`packages.statuses.${item.status.toLowerCase().replace(' ', '_')}`, { defaultValue: item.status })} />
                        )}
                        {'driverName' in item && (
                            <InfoRow label={t('packages.driver', { defaultValue: 'Driver' })} icon="person-circle-outline" value={`${item.driverName} (${item.driverPhone})`} />
                        )}
                        </YStack>
                        <Button mt="$3" size="$3" br="$6" variant="outlined" icon={<Ionicons name="eye-outline" size={18} />}
                          onPress={() => 
                            router.push({
                              pathname: "/packages/package-tracking",
                              params: { id: index.toString(), type: activeTab } // temporarly
                            })
                          }              
                        >
                          {t('packages.trackPackage', { defaultValue: 'Track Package' })}
                        </Button>

                    </Card>
                </MotiView>
                ))
          )}
          <Separator my="$4" />
        </YStack>
      </ScrollView>
    </>
  );
}

function InfoRow({
  label,
  icon,
  value,
}: {
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  value?: string;
}) {
  if (!value) return null;

  return (
    <XStack ai="center" gap="$3">
      <Ionicons name={icon} size={18} color="#888" />
      <Text color="$gray9" fontSize="$4">
        <Text color="$gray11" fontWeight="600">{label}:</Text> {value}
      </Text>
    </XStack>
  );
}
