/* import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import { useEffect } from 'react';

export default function Layout() {
  const [loaded] = useFonts({
    Inter: require('@tamagui/font-inter/otf/Inter-Medium.otf'),
    InterBold: require('@tamagui/font-inter/otf/Inter-Bold.otf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) return null;

  return (
    <Stack screenOptions={{headerShown: false}}/>
  );
}
 */

import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import { useEffect, useState } from 'react';
import { PortalProvider, TamaguiProvider } from 'tamagui';
import { I18nextProvider } from 'react-i18next';

import config from '../../../tamagui.config';
import i18n from '../../../i18n';
import { useLanguageStore } from '../../../stores/languageStore';
import { ErrorBoundary } from '../../../components/common/ErrorBoundary';
import { useAuthInitialization } from '../../../hooks/useAuthInitialization';

export default function Layout() {
  const [loaded] = useFonts({
    Inter: require('@tamagui/font-inter/otf/Inter-Medium.otf'),
    InterBold: require('@tamagui/font-inter/otf/Inter-Bold.otf'),
  });

  const [appReady, setAppReady] = useState(false);
  const { initializeLanguage } = useLanguageStore();

  useAuthInitialization();

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
      initializeLanguage().finally(() => {
        setAppReady(true);
      });
    }
  }, [loaded, initializeLanguage]);

  if (!loaded || !appReady) return null;

  return (
    <ErrorBoundary>
      <TamaguiProvider config={config}>
        <PortalProvider>
          <I18nextProvider i18n={i18n}>
            <Stack screenOptions={{ headerShown: false }} />
          </I18nextProvider>
        </PortalProvider>
      </TamaguiProvider>
    </ErrorBoundary>
  );
}
