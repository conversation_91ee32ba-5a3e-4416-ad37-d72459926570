import { useState } from 'react'
import { Dimensions, ScrollView, TouchableOpacity } from 'react-native'
import { Image, XStack, YStack } from 'tamagui'

const { width } = Dimensions.get('window')

type Props = {
  images: string[]
}

export const ImageGallery = ({ images }: Props) => {
  const [active, setActive] = useState(0)

  return (
    <YStack gap="$2" width="100%">
      {/* Main hero image */}
      <Image
        source={{ uri: images[active] }}
        width="100%"
        height={400}
        borderRadius="$0"
      />

      {/* Thumbnail row */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingVertical: 4 }}
      >
        <XStack gap="$2" px="$2">
          {images.map((img, idx) => (
            <TouchableOpacity key={img} onPress={() => setActive(idx)}>
              <Image
                source={{ uri: img }}
                width={70}
                height={70}
                borderRadius="$4"
                bw={active === idx ? "$1" : "$0"}
                boc={active === idx ? "$primary" : "transparent"}
              />
            </TouchableOpacity>
          ))}
        </XStack>
      </ScrollView>
    </YStack>
  )
}
