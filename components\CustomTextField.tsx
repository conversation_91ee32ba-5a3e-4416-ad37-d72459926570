import * as React from 'react'
import { Input, Text, XStack, YStack, Label } from 'tamagui'
import { useController, useFormContext } from 'react-hook-form'
import { Ionicons } from '@expo/vector-icons'

type Props = {
  name?: string
  icon?: string
  label?: string
  placeholder?: string
  secureTextEntry?: boolean
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad'
  // New props for direct usage (without form context)
  value?: string
  onChangeText?: (text: string) => void
  required?: boolean
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'
  rightIcon?: React.ReactNode
}

export const CustomTextField: React.FC<Props> = ({
  name,
  icon,
  label,
  placeholder,
  secureTextEntry,
  keyboardType = 'default',
  value: directValue,
  onChangeText: directOnChangeText,
  required,
  autoCapitalize,
  rightIcon
}) => {
  // Try to get form context, but don't require it
  let formContext;
  try {
    formContext = useFormContext();
  } catch {
    formContext = null;
  }

  // Use form controller if form context exists and name is provided
  let fieldProps;
  if (formContext && name) {
    const {
      field: { onChange, onBlur, value },
      fieldState: { error }
    } = useController({ name, control: formContext.control });
    fieldProps = { onChange, onBlur, value, error };
  } else {
    fieldProps = {
      onChange: directOnChangeText,
      onBlur: () => {},
      value: directValue || '',
      error: null
    };
  }

  return (
    <YStack gap="$2" width="100%">
      {label && (
        <Label fontSize="$4" fontWeight="600" color="$gray11">
          {label} {required && <Text color="$red9">*</Text>}
        </Label>
      )}

      <XStack
        alignItems="center"
        gap="$3"
        padding="$3"
        borderWidth={2}
        borderColor={fieldProps.error ? '$red7' : '$gray6'}
        borderRadius="$4"
        backgroundColor="$background"
        focusStyle={{
          borderColor: '$primary'
        }}
      >
        {icon && (
          <Ionicons name={icon as any} size={20} color="#7529B3" />
        )}

        <Input
          flex={1}
          fontSize="$4"
          placeholder={placeholder}
          onChangeText={fieldProps.onChange}
          onBlur={fieldProps.onBlur}
          value={fieldProps.value}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          borderWidth={0}
          backgroundColor="transparent"
          focusStyle={{
            borderWidth: 0,
            backgroundColor: "transparent"
          }}
        />

        {rightIcon}
      </XStack>

      {fieldProps.error && (
        <Text color="$red9" fontSize="$3">
          {fieldProps.error.message}
        </Text>
      )}
    </YStack>
  )
}
